import React from 'react';
import {
  Spinner,
  Center,
  VStack,
  Text,
} from '@chakra-ui/react';

interface LoadingSpinnerProps {
  message?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  message = 'Loading...', 
  fullScreen = false 
}) => {
  const content = (
    <VStack spacing={4}>
      <Spinner
        thickness="4px"
        speed="0.65s"
        emptyColor="gray.200"
        color="primary.500"
        size="xl"
      />
      <Text color="gray.600" fontSize="lg">
        {message}
      </Text>
    </VStack>
  );

  if (fullScreen) {
    return (
      <Center 
        position="fixed"
        top={0}
        left={0}
        right={0}
        bottom={0}
        bg="white"
        zIndex={9999}
      >
        {content}
      </Center>
    );
  }

  return (
    <Center py={8}>
      {content}
    </Center>
  );
};

export default LoadingSpinner;