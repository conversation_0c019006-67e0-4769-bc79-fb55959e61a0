import React from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  VStack,
  Text,
  Icon,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiAward } from 'react-icons/fi';
import { motion } from 'framer-motion';

const MotionIcon = motion(Icon);

interface LevelUpNotificationProps {
  isOpen: boolean;
  onClose: () => void;
  level: number;
}

const LevelUpNotification: React.FC<LevelUpNotificationProps> = ({
  isOpen,
  onClose,
  level,
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  
  React.useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(onClose, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent
        bg={bgColor}
        borderRadius="xl"
        p={6}
        maxW="sm"
        mx={4}
      >
        <ModalBody>
          <VStack spacing={4}>
            <MotionIcon
              as={FiAward}
              boxSize={12}
              color="yellow.500"
              initial={{ scale: 0 }}
              animate={{
                scale: [0, 1.2, 1],
                rotate: [0, -10, 10, 0],
              }}
              transition={{
                duration: 0.5,
                ease: "easeOut",
              }}
            />
            <VStack spacing={2}>
              <Text
                fontSize="2xl"
                fontWeight="bold"
                color="yellow.500"
                textAlign="center"
              >
                Level Up!
              </Text>
              <Text
                fontSize="lg"
                color="gray.600"
                textAlign="center"
              >
                You've reached Level {level}
              </Text>
              <Text
                fontSize="sm"
                color="gray.500"
                textAlign="center"
              >
                Keep up the great work!
              </Text>
            </VStack>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default LevelUpNotification;