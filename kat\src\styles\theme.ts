import { theme as baseTheme } from '@chakra-ui/theme'
import type { ThemeConfig } from '@chakra-ui/theme'
import { extendTheme } from '@chakra-ui/react'

const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: false,
}

const colors = {
  primary: {
    50: '#E6F6FF',
    100: '#BAE3FF',
    200: '#7CC4FA',
    300: '#47A3F3',
    400: '#2186EB',
    500: '#0967D2',
    600: '#0552B5',
    700: '#03449E',
    800: '#01337D',
    900: '#002159',
  },
  secondary: {
    50: '#FFE3EC',
    100: '#FFB8D2',
    200: '#FF8CBA',
    300: '#F364A2',
    400: '#E8368F',
    500: '#DA127D',
    600: '#BC0A6F',
    700: '#A30664',
    800: '#870557',
    900: '#620042',
  },
}

const theme = extendTheme({
  config,
  colors: {
    ...baseTheme.colors,
    ...colors,
  },
  fonts: {
    heading: '"Fredoka One", system-ui, sans-serif',
    body: '"Comic Sans MS", "Comic Sans", system-ui, sans-serif',
  },
  components: {
    Button: {
      baseStyle: {
        borderRadius: 'full',
        fontWeight: 'bold',
      },
      variants: {
        solid: {
          _hover: {
            transform: 'scale(1.05)',
          },
          _active: {
            transform: 'scale(0.95)',
          },
        },
      },
      defaultProps: {
        colorScheme: 'primary',
      },
    },
  },
})

export default theme