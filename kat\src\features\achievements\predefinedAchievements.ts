import { CreateAchievementData } from '../../types/achievement';

export const ACHIEVEMENTS: CreateAchievementData[] = [
  {
    title: 'First Steps',
    description: 'Complete your first task',
    imageUrl: '🎯',
    condition: {
      type: 'TASKS_COMPLETED',
      value: 1,
    },
  },
  {
    title: 'Task Master',
    description: 'Complete 10 tasks',
    imageUrl: '🏆',
    condition: {
      type: 'TASKS_COMPLETED',
      value: 10,
    },
  },
  {
    title: 'Super Achiever',
    description: 'Complete 50 tasks',
    imageUrl: '🌟',
    condition: {
      type: 'TASKS_COMPLETED',
      value: 50,
    },
  },
  {
    title: 'Point Collector',
    description: 'Earn 100 points',
    imageUrl: '💎',
    condition: {
      type: 'POINTS_EARNED',
      value: 100,
    },
  },
  {
    title: 'Point Master',
    description: 'Earn 500 points',
    imageUrl: '💰',
    condition: {
      type: 'POINTS_EARNED',
      value: 500,
    },
  },
  {
    title: 'Daily Hero',
    description: 'Maintain a 3-day streak',
    imageUrl: '🔥',
    condition: {
      type: 'STREAK_MAINTAINED',
      value: 3,
    },
  },
  {
    title: 'Weekly Champion',
    description: 'Maintain a 7-day streak',
    imageUrl: '⭐',
    condition: {
      type: 'STREAK_MAINTAINED',
      value: 7,
    },
  },
  {
    title: 'Monthly Legend',
    description: 'Maintain a 30-day streak',
    imageUrl: '👑',
    condition: {
      type: 'STREAK_MAINTAINED',
      value: 30,
    },
  },
];

export const getAchievementImageUrl = (achievementId: string): string => {
  const achievement = ACHIEVEMENTS.find(a => a.title === achievementId);
  return achievement?.imageUrl || '🎯';
};