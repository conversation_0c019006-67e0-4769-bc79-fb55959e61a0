import { useRef, useEffect } from 'react';

export const useFocusTrap = (isActive: boolean = true) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isActive) return;

    const container = containerRef.current;
    if (!container) return;

    // Get all focusable elements
    const focusableElements = container.querySelectorAll<HTMLElement>(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    // Function to handle circular tab navigation
    const handleTabKey = (e: KeyboardEvent) => {
      if (!isActive) return;

      const isTabPressed = e.key === 'Tab';
      if (!isTabPressed) return;

      if (e.shiftKey) {
        // If shift + tab and first element is focused, move to last
        if (document.activeElement === firstFocusable) {
          e.preventDefault();
          lastFocusable?.focus();
        }
      } else {
        // If tab and last element is focused, move to first
        if (document.activeElement === lastFocusable) {
          e.preventDefault();
          firstFocusable?.focus();
        }
      }
    };

    // Function to handle escape key
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (!isActive) return;

      if (e.key === 'Escape') {
        // Return focus to the last focused element before trap was activated
        const lastFocused = document.querySelector('[data-last-focused="true"]') as HTMLElement;
        if (lastFocused) {
          lastFocused.focus();
        }
      }
    };

    // Save current focused element
    const previouslyFocused = document.activeElement as HTMLElement;
    if (previouslyFocused) {
      previouslyFocused.setAttribute('data-last-focused', 'true');
    }

    // Focus first element when trap is activated
    firstFocusable?.focus();

    // Add event listeners
    document.addEventListener('keydown', handleTabKey);
    document.addEventListener('keydown', handleEscapeKey);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleTabKey);
      document.removeEventListener('keydown', handleEscapeKey);
      
      // Remove marker from previously focused element
      if (previouslyFocused) {
        previouslyFocused.removeAttribute('data-last-focused');
      }

      // Return focus when trap is deactivated
      if (previouslyFocused && document.body.contains(previouslyFocused)) {
        previouslyFocused.focus();
      }
    };
  }, [isActive]);

  return containerRef;
};