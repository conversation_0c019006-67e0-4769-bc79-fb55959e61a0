import { STORAGE_KEYS, StorageManager } from './storage';

export const DEFAULT_PIN = '1234';

export interface ParentStorageData {
  pin: string;
  isLocked: boolean;
  allowedFeatures: {
    taskCreation: boolean;
    profileCreation: boolean;
    rewardRedemption: boolean;
  };
}

export const getDefaultParentSettings = (): ParentStorageData => ({
  pin: DEFAULT_PIN,
  isLocked: true,
  allowedFeatures: {
    taskCreation: true,
    profileCreation: true,
    rewardRedemption: true,
  },
});

export const resetParentSettings = () => {
  const defaultSettings = getDefaultParentSettings();
  StorageManager.set(STORAGE_KEYS.PARENT_SETTINGS, defaultSettings);
  return defaultSettings;
};

export const getParentSettings = (): ParentStorageData => {
  return StorageManager.get(STORAGE_KEYS.PARENT_SETTINGS, getDefaultParentSettings());
};
