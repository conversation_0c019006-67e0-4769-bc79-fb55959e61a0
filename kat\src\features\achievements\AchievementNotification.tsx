import React, { useEffect } from 'react';
import {
  Box,
  Text,
  SlideFade,
  VStack,
  Heading,
  useDisclosure,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import { selectLastUnlockedAchievement, clearLastUnlocked } from '../../store/slices/achievementSlice';

const MotionBox = motion(Box);

const AchievementNotification = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const lastUnlocked = useSelector(selectLastUnlockedAchievement);
  const dispatch = useDispatch();

  useEffect(() => {
    if (lastUnlocked) {
      onOpen();
      const timer = setTimeout(() => {
        onClose();
        dispatch(clearLastUnlocked());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [lastUnlocked, onOpen, onClose, dispatch]);

  if (!lastUnlocked) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <MotionBox
          position="fixed"
          top="20px"
          right="20px"
          zIndex={9999}
          initial={{ y: -100, opacity: 0 }}
          animate={{
            y: 0,
            opacity: 1,
            transition: { type: 'spring', damping: 15, stiffness: 200 },
          }}
          exit={{ y: -100, opacity: 0 }}
        >
          <Box
            bg="white"
            borderRadius="xl"
            boxShadow="2xl"
            p={4}
            minW="300px"
            border="2px solid"
            borderColor="yellow.400"
            position="relative"
            _before={{
              content: '""',
              position: 'absolute',
              top: '-10px',
              left: '-10px',
              right: '-10px',
              bottom: '-10px',
              background: 'linear-gradient(45deg, yellow.200, orange.200)',
              borderRadius: 'xl',
              zIndex: -1,
              opacity: 0.3,
              filter: 'blur(10px)',
            }}
          >
            <VStack spacing={2} align="center">
              <Text
                fontSize="sm"
                fontWeight="bold"
                color="yellow.500"
                textTransform="uppercase"
              >
                Achievement Unlocked!
              </Text>
              <Text fontSize="4xl" lineHeight={1}>
                {lastUnlocked.imageUrl}
              </Text>
              <Heading size="md" color="gray.700">
                {lastUnlocked.title}
              </Heading>
              <Text fontSize="sm" color="gray.500" textAlign="center">
                {lastUnlocked.description}
              </Text>
            </VStack>
          </Box>
        </MotionBox>
      )}
    </AnimatePresence>
  );
};

export default AchievementNotification;