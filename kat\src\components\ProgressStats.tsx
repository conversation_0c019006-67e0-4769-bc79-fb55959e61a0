import {
  Box,
  CircularProgress,
  CircularProgressLabel,
  Grid,
  Text,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react';
import { useSelector } from 'react-redux';
import { RootState } from '../store';

const ProgressCard = ({
  label,
  value,
  total,
  color,
}: {
  label: string;
  value: number;
  total: number;
  color: string;
}) => {
  const percentage = Math.round((value / total) * 100) || 0;
  const bgColor = useColorModeValue('white', 'gray.800');

  return (
    <Box p={4} bg={bgColor} borderRadius="xl" boxShadow="sm">
      <VStack spacing={2}>
        <CircularProgress
          value={percentage}
          color={color}
          size="100px"
          thickness="8px"
        >
          <CircularProgressLabel>{percentage}%</CircularProgressLabel>
        </CircularProgress>
        <Text fontWeight="bold" color="gray.700">
          {label}
        </Text>
        <Text fontSize="sm" color="gray.500">
          {value} / {total}
        </Text>
      </VStack>
    </Box>
  );
};

const ProgressStats = () => {
  const selectedProfileId = useSelector(
    (state: RootState) => state.profile.selectedProfileId
  );
  const tasks = useSelector((state: RootState) => state.task.tasks);
  const achievements = useSelector(
    (state: RootState) => state.achievement.achievements
  );
  const userAchievements = useSelector(
    (state: RootState) => state.achievement.userAchievements
  );

  if (!selectedProfileId) return null;

  const profileTasks = tasks.filter(
    (task) => task.assignedTo.includes(selectedProfileId)
  );
  const completedTasks = profileTasks.filter((task) => task.completed);

  const userProfileAchievements = userAchievements.filter(
    (ua) => ua.profileId === selectedProfileId
  );

  // Get today's tasks
  const today = new Date().toDateString();
  const todaysTasks = profileTasks.filter(
    (task) =>
      task.createdAt && new Date(task.createdAt).toDateString() === today
  );
  const todaysCompletedTasks = todaysTasks.filter((task) => task.completed);

  return (
    <Grid
      templateColumns={{ base: '1fr', md: 'repeat(3, 1fr)' }}
      gap={6}
      w="100%"
    >
      <ProgressCard
        label="Today's Tasks"
        value={todaysCompletedTasks.length}
        total={todaysTasks.length}
        color="green.400"
      />
      <ProgressCard
        label="Total Tasks"
        value={completedTasks.length}
        total={profileTasks.length}
        color="blue.400"
      />
      <ProgressCard
        label="Achievements"
        value={userProfileAchievements.length}
        total={achievements.length}
        color="purple.400"
      />
    </Grid>
  );
};

export default ProgressStats;