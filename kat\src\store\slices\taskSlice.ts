import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { v4 as uuidv4 } from 'uuid'
import type { Task, CreateTaskData, UpdateTaskData } from '../../types/task'
import { STORAGE_KEYS, StorageManager } from '../../utils/storage'

interface TaskState {
  tasks: Task[]
  isLoading: boolean
  error: string | null
}

const initialState: TaskState = {
  tasks: StorageManager.get<Task[]>(STORAGE_KEYS.TASKS, []),
  isLoading: false,
  error: null,
}

const taskSlice = createSlice({
  name: 'task',
  initialState,
  reducers: {
    createTask: (state, action: PayloadAction<CreateTaskData>) => {
      const newTask: Task = {
        id: uuidv4(),
        title: action.payload.title,
        description: action.payload.description,
        reward: action.payload.reward,
        assignedTo: action.payload.assignedTo,
        completed: false,
        createdAt: Date.now(),
      }
      state.tasks.push(newTask)
      StorageManager.set(STORAGE_KEYS.TASKS, state.tasks)
    },
    updateTask: (state, action: PayloadAction<{ id: string; data: UpdateTaskData }>) => {
      const task = state.tasks.find(t => t.id === action.payload.id)
      if (task) {
        Object.assign(task, action.payload.data)
        StorageManager.set(STORAGE_KEYS.TASKS, state.tasks)
      }
    },
    completeTask: (state, action: PayloadAction<string>) => {
      const task = state.tasks.find(t => t.id === action.payload)
      if (task && !task.completed) {
        task.completed = true
        task.completedAt = Date.now()
        StorageManager.set(STORAGE_KEYS.TASKS, state.tasks)
      }
    },
    deleteTask: (state, action: PayloadAction<string>) => {
      state.tasks = state.tasks.filter(t => t.id !== action.payload)
      StorageManager.set(STORAGE_KEYS.TASKS, state.tasks)
    },
    resetTask: (state, action: PayloadAction<string>) => {
      const task = state.tasks.find(t => t.id === action.payload)
      if (task) {
        task.completed = false
        task.completedAt = undefined
        StorageManager.set(STORAGE_KEYS.TASKS, state.tasks)
      }
    },
  },
})

export const {
  createTask,
  updateTask,
  completeTask,
  deleteTask,
  resetTask,
} = taskSlice.actions

export default taskSlice.reducer