export interface Achievement {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  condition: {
    type: 'TASKS_COMPLETED' | 'POINTS_EARNED' | 'STREAK_MAINTAINED';
    value: number;
  };
  unlockedAt?: number;
}

export interface UserAchievement {
  profileId: string;
  achievementId: string;
  unlockedAt: number;
}

export type CreateAchievementData = Omit<Achievement, 'id' | 'unlockedAt'>;