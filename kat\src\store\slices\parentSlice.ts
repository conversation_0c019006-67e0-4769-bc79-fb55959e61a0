import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ParentSettings } from '../../types/parent';
import type { RootState } from '../index';
import { 
  getParentSettings, 
  resetParentSettings, 
  DEFAULT_PIN,
  ParentStorageData 
} from '../../utils/parentUtils';

const MAX_PIN_ATTEMPTS = 3;
const LOCK_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

interface ParentState {
  settings: ParentStorageData;
  pinAttempts: number;
  isTemporarilyLocked: boolean;
  lockExpiresAt: number | null;
  lastValidationResult: {
    isValid: boolean;
    attemptsLeft?: number;
    isTemporarilyLocked?: boolean;
    lockExpiresAt?: number;
  } | null;
  needsInitialSetup: boolean;
}

const savedSettings = getParentSettings();

const initialState: ParentState = {
  settings: savedSettings,
  pinAttempts: 0,
  isTemporarilyLocked: false,
  lockExpiresAt: null,
  lastValidationResult: null,
  needsInitialSetup: false,
};

const parentSlice = createSlice({
  name: 'parent',
  initialState,
  reducers: {
    setPin: (state, action: PayloadAction<string>) => {
      state.settings = {
        ...state.settings,
        pin: action.payload,
      };
      state.needsInitialSetup = false;
      resetParentSettings();
    },
    attemptPinValidation: (state, action: PayloadAction<string>) => {
      if (state.isTemporarilyLocked) {
        const now = Date.now();
        if (state.lockExpiresAt && now < state.lockExpiresAt) {
          state.lastValidationResult = {
            isValid: false,
            isTemporarilyLocked: true,
            lockExpiresAt: state.lockExpiresAt,
          };
          return;
        }
        state.isTemporarilyLocked = false;
        state.lockExpiresAt = null;
        state.pinAttempts = 0;
      }

      const isValid = action.payload === state.settings.pin;
      
      if (isValid) {
        state.settings.isLocked = false;
        state.pinAttempts = 0;
        state.lastValidationResult = { isValid: true };
        resetParentSettings();
        return;
      }

      state.pinAttempts += 1;
      
      if (state.pinAttempts >= MAX_PIN_ATTEMPTS) {
        state.isTemporarilyLocked = true;
        state.lockExpiresAt = Date.now() + LOCK_DURATION;
        state.lastValidationResult = {
          isValid: false,
          isTemporarilyLocked: true,
          lockExpiresAt: state.lockExpiresAt,
        };
        return;
      }

      state.lastValidationResult = {
        isValid: false,
        attemptsLeft: MAX_PIN_ATTEMPTS - state.pinAttempts,
      };
    },
    lockParentControls: (state) => {
      state.settings = {
        ...state.settings,
        isLocked: true,
      };
      resetParentSettings();
    },
    updateAllowedFeatures: (state, action: PayloadAction<Partial<ParentStorageData['allowedFeatures']>>) => {
      state.settings = {
        ...state.settings,
        allowedFeatures: {
          ...state.settings.allowedFeatures,
          ...action.payload,
        }
      };
      resetParentSettings();
    },
    resetPinAttempts: (state) => {
      state.pinAttempts = 0;
      state.isTemporarilyLocked = false;
      state.lockExpiresAt = null;
      state.lastValidationResult = null;
    },
    clearValidationResult: (state) => {
      state.lastValidationResult = null;
    },
    resetPin: (state) => {
      const defaultSettings = resetParentSettings();
      state.settings = defaultSettings;
      state.pinAttempts = 0;
      state.isTemporarilyLocked = false;
      state.lockExpiresAt = null;
      state.lastValidationResult = null;
    },
  },
});

// Selectors
export const selectParentSettings = (state: RootState) => state.parent.settings;
export const selectPinValidationResult = (state: RootState) => state.parent.lastValidationResult;
export const selectIsParentLocked = (state: RootState) => state.parent.settings.isLocked;
export const selectAllowedFeatures = (state: RootState) => state.parent.settings.allowedFeatures;
export const selectNeedsInitialSetup = (state: RootState) => state.parent.needsInitialSetup;

export const {
  setPin,
  attemptPinValidation,
  lockParentControls,
  updateAllowedFeatures,
  resetPinAttempts,
  clearValidationResult,
  resetPin,
} = parentSlice.actions;

export default parentSlice.reducer;