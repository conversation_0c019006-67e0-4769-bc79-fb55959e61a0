import {
  Box,
  Button,
  Heading,
  SimpleGrid,
  Text,
  VStack,
  useColorModeValue,
  Avatar,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  useDisclosure,
  Wrap,
  WrapItem,
  useToast,
} from '@chakra-ui/react'
import { useNavigate } from 'react-router-dom'
import { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import type { RootState } from '../../store'
import { createProfile, selectProfile } from '../../store/slices/profileSlice'
import type { CreateProfileData, Profile } from '../../types/profile'
import { useParentControls } from '../../hooks/useParentControls'

const ProfileSelection = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const toast = useToast()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [newProfileName, setNewProfileName] = useState('')
  const [selectedColor, setSelectedColor] = useState('hsl(210, 70%, 50%)')
  const { canCreateProfiles } = useParentControls()
  
  const profiles = useSelector((state: RootState) => state.profile.profiles)

  const avatarColors = [
    'hsl(210, 70%, 50%)', // Blue
    'hsl(340, 70%, 50%)', // Pink
    'hsl(160, 70%, 50%)', // Green
    'hsl(45, 70%, 50%)',  // Gold
    'hsl(280, 70%, 50%)', // Purple
    'hsl(15, 70%, 50%)',  // Orange
    'hsl(190, 70%, 50%)', // Cyan
    'hsl(320, 70%, 50%)', // Magenta
  ]
  const selectedProfileId = useSelector((state: RootState) => state.profile.selectedProfileId)

  const bgGradient = useColorModeValue(
    'linear(to-b, primary.100, white)',
    'linear(to-b, primary.900, gray.800)'
  )

  const handleSelectProfile = (profileId: string) => {
    dispatch(selectProfile(profileId))
    navigate('/')
  }

  const handleCreateProfile = () => {
    if (!canCreateProfiles) {
      toast({
        title: 'Access Denied',
        description: 'Profile creation is disabled in parent controls',
        status: 'error',
        duration: 3000,
      })
      onClose()
      return
    }

    if (newProfileName.trim()) {
      const newProfile: CreateProfileData = {
        name: newProfileName.trim(),
        avatarColor: selectedColor,
      }
      dispatch(createProfile(newProfile))
      setNewProfileName('')
      setSelectedColor('hsl(210, 70%, 50%)') // Reset to default blue
      onClose()
    }
  }

  const handleCreateClick = () => {
    if (!canCreateProfiles) {
      toast({
        title: 'Access Denied',
        description: 'Profile creation is disabled in parent controls',
        status: 'error',
        duration: 3000,
      })
      return
    }
    onOpen()
  }

  return (
    <Box py={10}>
      <VStack spacing={8}>
        <Heading size="2xl" color="primary.500">
          Choose Your Profile
        </Heading>
        <Text fontSize="xl" color="gray.600">
          Select your profile {canCreateProfiles && 'or create a new one'}
        </Text>

        <SimpleGrid
          columns={{ base: 1, sm: 2, md: 3 }}
          spacing={6}
          width="full"
          maxW="4xl"
          py={10}
        >
          {profiles.map((profile: Profile) => (
            <Button
              key={profile.id}
              height="200px"
              bgGradient={bgGradient}
              onClick={() => handleSelectProfile(profile.id)}
              _hover={{
                transform: 'translateY(-5px)',
                boxShadow: 'xl',
              }}
              _active={{
                transform: 'translateY(0)',
                boxShadow: 'md',
              }}
              transition="all 0.2s"
              display="flex"
              flexDirection="column"
              gap={4}
              border={profile.id === selectedProfileId ? '3px solid' : undefined}
              borderColor="primary.500"
            >
              <Avatar
                size="lg"
                name={profile.name}
                bg={profile.avatarColor}
              />
              <Text>{profile.name}</Text>
              <Text fontSize="sm" color="gray.500">
                {profile.points} points
              </Text>
            </Button>
          ))}

          {/* New Profile Button */}
          <Button
            height="200px"
            bgGradient={bgGradient}
            onClick={handleCreateClick}
            _hover={canCreateProfiles ? {
              transform: 'translateY(-5px)',
              boxShadow: 'xl',
            } : undefined}
            _active={canCreateProfiles ? {
              transform: 'translateY(0)',
              boxShadow: 'md',
            } : undefined}
            transition="all 0.2s"
            display="flex"
            flexDirection="column"
            gap={4}
            opacity={canCreateProfiles ? 1 : 0.5}
            cursor={canCreateProfiles ? 'pointer' : 'not-allowed'}
          >
            <Text fontSize="5xl">+</Text>
            <Text>Create New Profile</Text>
            {!canCreateProfiles && (
              <Text fontSize="sm" color="red.500">
                (Disabled)
              </Text>
            )}
          </Button>
        </SimpleGrid>
      </VStack>

      {/* Create Profile Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Create New Profile</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={6}>
              <FormControl>
                <FormLabel>Name</FormLabel>
                <Input
                  value={newProfileName}
                  onChange={(e) => setNewProfileName(e.target.value)}
                  placeholder="Enter profile name"
                />
              </FormControl>

              <Box textAlign="center">
                <Avatar
                  size="xl"
                  name={newProfileName || "Preview"}
                  bg={selectedColor}
                  mb={4}
                />
                <FormControl>
                  <FormLabel>Choose Avatar Color</FormLabel>
                  <Wrap spacing={2} justify="center">
                    {avatarColors.map((color) => (
                      <WrapItem key={color}>
                        <Button
                          w="40px"
                          h="40px"
                          p={0}
                          bg={color}
                          _hover={{ transform: 'scale(1.1)' }}
                          onClick={() => setSelectedColor(color)}
                          border={selectedColor === color ? '3px solid' : 'none'}
                          borderColor="primary.500"
                          aria-label={`Select ${color} as avatar color`}
                        />
                      </WrapItem>
                    ))}
                  </Wrap>
                </FormControl>
              </Box>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="primary"
              onClick={handleCreateProfile}
              isDisabled={!newProfileName.trim() || !canCreateProfiles}
            >
              Create Profile
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}

export default ProfileSelection