import React, { useState, useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  PinInput,
  PinInputField,
  HStack,
  Text,
  VStack,
  useToast,
  Alert,
  AlertIcon,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
} from '@chakra-ui/react';
import { useDispatch, useSelector } from 'react-redux';
import {
  attemptPinValidation,
  selectPinValidationResult,
  clearValidationResult,
  selectNeedsInitialSetup,
  setPin,
  resetPin,
} from '../../store/slices/parentSlice';

interface PinProtectionProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  title?: string;
}

const PinProtection: React.FC<PinProtectionProps> = ({
  isOpen,
  onClose,
  onSuccess,
  title = 'Parent PIN Required',
}) => {
  const [pin, setInputPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [isPinConfirmation, setIsPinConfirmation] = useState(false);
  const dispatch = useDispatch();
  const toast = useToast();
  const validationResult = useSelector(selectPinValidationResult);
  const needsInitialSetup = useSelector(selectNeedsInitialSetup);
  const {
    isOpen: isResetAlertOpen,
    onOpen: onResetAlertOpen,
    onClose: onResetAlertClose,
  } = useDisclosure();
  const cancelRef = React.useRef<HTMLButtonElement>(null) as React.RefObject<HTMLButtonElement>;

  useEffect(() => {
    if (validationResult) {
      if (validationResult.isValid) {
        onSuccess();
        onClose();
        toast({
          title: 'Access granted',
          status: 'success',
          duration: 2000,
        });
      } else if (validationResult.isTemporarilyLocked) {
        const remainingTime = Math.ceil(
          (validationResult.lockExpiresAt! - Date.now()) / 1000 / 60
        );
        toast({
          title: 'Too many attempts',
          description: `Try again in ${remainingTime} minutes`,
          status: 'error',
          duration: 5000,
        });
      } else if (validationResult.attemptsLeft !== undefined) {
        toast({
          title: 'Incorrect PIN',
          description: `${validationResult.attemptsLeft} attempts remaining`,
          status: 'error',
          duration: 3000,
        });
      }
      dispatch(clearValidationResult());
    }
  }, [validationResult, dispatch, onSuccess, onClose, toast]);

  const handlePinComplete = (value: string) => {
    if (needsInitialSetup) {
      if (!isPinConfirmation) {
        setInputPin(value);
        setIsPinConfirmation(true);
        setConfirmPin('');
      } else {
        if (value === pin) {
          dispatch(setPin(value));
          onSuccess();
          onClose();
          toast({
            title: 'PIN created successfully',
            status: 'success',
            duration: 2000,
          });
        } else {
          toast({
            title: 'PINs do not match',
            description: 'Please try again',
            status: 'error',
            duration: 3000,
          });
          setIsPinConfirmation(false);
          setInputPin('');
          setConfirmPin('');
        }
      }
    } else {
      dispatch(attemptPinValidation(value));
    }
  };

  const handleClose = () => {
    setInputPin('');
    setConfirmPin('');
    setIsPinConfirmation(false);
    onClose();
  };

  const handleResetPin = () => {
    dispatch(resetPin());
    toast({
      title: 'PIN Reset',
      description: 'PIN has been reset to default (1234)',
      status: 'success',
      duration: 5000,
    });
    handleClose();
    onResetAlertClose();
  };

  return (
    <>
      <Modal 
        isOpen={isOpen} 
        onClose={handleClose} 
        isCentered
      >
        <ModalOverlay />
        <ModalContent mx={2} w={{ base: "95%", sm: "400px" }}>
          <ModalHeader>{title}</ModalHeader>
          <ModalBody>
            <VStack spacing={4}>
              {needsInitialSetup ? (
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <VStack align="start" spacing={1}>
                    <Text>The default PIN is: <strong>1234</strong></Text>
                    <Text>You can change it in the parent settings later.</Text>
                  </VStack>
                </Alert>
              ) : null}
              <Text>{!needsInitialSetup && 'Enter the parent PIN to continue'}</Text>
              <HStack>
                <PinInput
                  type="number"
                  mask
                  value={isPinConfirmation ? confirmPin : pin}
                  onChange={isPinConfirmation ? setConfirmPin : setInputPin}
                  onComplete={handlePinComplete}
                >
                  <PinInputField />
                  <PinInputField />
                  <PinInputField />
                  <PinInputField />
                </PinInput>
              </HStack>
              {!needsInitialSetup && (
                <Button
                  variant="link"
                  colorScheme="blue"
                  size="sm"
                  onClick={onResetAlertOpen}
                >
                  Forgot PIN?
                </Button>
              )}
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <AlertDialog
        isOpen={isResetAlertOpen}
        leastDestructiveRef={cancelRef}
        onClose={onResetAlertClose}
        isCentered
      >
        <AlertDialogOverlay>
          <AlertDialogContent mx={2}>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Reset PIN
            </AlertDialogHeader>

            <AlertDialogBody>
              Are you sure? This will reset your PIN to the default (1234).
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onResetAlertClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleResetPin} ml={3}>
                Reset PIN
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default PinProtection;