import { useCallback, useRef } from 'react';

interface AnnouncementOptions {
  politeness?: 'polite' | 'assertive';
  clearAfter?: number;
}

export const useAnnouncement = () => {
  const politeRef = useRef<HTMLDivElement | null>(null);
  const assertiveRef = useRef<HTMLDivElement | null>(null);

  const announce = useCallback((
    message: string,
    options: AnnouncementOptions = {}
  ) => {
    const {
      politeness = 'polite',
      clearAfter = 5000, // Default 5 seconds
    } = options;

    const targetRef = politeness === 'polite' ? politeRef : assertiveRef;
    if (!targetRef.current) {
      // Create live region if it doesn't exist
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', politeness);
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only'; // Screen reader only
      document.body.appendChild(liveRegion);
      targetRef.current = liveRegion;
    }

    // Set the message
    targetRef.current.textContent = message;

    // Clear after specified duration
    if (clearAfter > 0) {
      setTimeout(() => {
        if (targetRef.current) {
          targetRef.current.textContent = '';
        }
      }, clearAfter);
    }
  }, []);

  // Helper functions for common announcements
  const announcePolite = useCallback((message: string, clearAfter?: number) => {
    announce(message, { politeness: 'polite', clearAfter });
  }, [announce]);

  const announceAssertive = useCallback((message: string, clearAfter?: number) => {
    announce(message, { politeness: 'assertive', clearAfter });
  }, [announce]);

  return {
    announce,
    announcePolite,
    announceAssertive,
  };
};

// Add this to your global CSS
const styles = `
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
`;

// Create and inject styles
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);