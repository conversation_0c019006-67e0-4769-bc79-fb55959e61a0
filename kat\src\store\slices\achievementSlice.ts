import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Achievement, UserAchievement } from '../../types/achievement';
import { STORAGE_KEYS, StorageManager } from '../../utils/storage';
import { playSound } from '../../utils/sounds';
import { ACHIEVEMENTS } from '../../features/achievements/predefinedAchievements';
import type { RootState } from '../index';

interface AchievementState {
  achievements: Achievement[];
  userAchievements: UserAchievement[];
  lastUnlocked: Achievement | null;
}

// Initialize achievements with IDs
const initialAchievements = ACHIEVEMENTS.map((achievement) => ({
  ...achievement,
  id: achievement.title.toLowerCase().replace(/\s+/g, '-'),
}));

const initialState: AchievementState = {
  achievements: StorageManager.get(STORAGE_KEYS.ACHIEVEMENTS, initialAchievements),
  userAchievements: StorageManager.get(STORAGE_KEYS.USER_ACHIEVEMENTS, []),
  lastUnlocked: null,
};

const achievementSlice = createSlice({
  name: 'achievement',
  initialState,
  reducers: {
    unlockAchievement: (
      state,
      action: PayloadAction<{ profileId: string; achievementId: string }>
    ) => {
      const { profileId, achievementId } = action.payload;
      const existingAchievement = state.userAchievements.find(
        (ua) => ua.profileId === profileId && ua.achievementId === achievementId
      );

      if (!existingAchievement) {
        const achievement = state.achievements.find(a => a.id === achievementId);
        if (achievement) {
          state.userAchievements.push({
            profileId,
            achievementId,
            unlockedAt: Date.now(),
          });
          state.lastUnlocked = achievement;
          StorageManager.set(STORAGE_KEYS.USER_ACHIEVEMENTS, state.userAchievements);
          playSound('achievement');
        }
      }
    },
    clearLastUnlocked: (state) => {
      state.lastUnlocked = null;
    },
    checkTasksCompletedAchievements: (
      state,
      action: PayloadAction<{ profileId: string; completedCount: number }>
    ) => {
      const { profileId, completedCount } = action.payload;
      const taskAchievements = state.achievements.filter(
        (a) => a.condition.type === 'TASKS_COMPLETED' && a.condition.value <= completedCount
      );

      taskAchievements.forEach((achievement) => {
        const isUnlocked = state.userAchievements.some(
          (ua) => ua.profileId === profileId && ua.achievementId === achievement.id
        );

        if (!isUnlocked) {
          state.userAchievements.push({
            profileId,
            achievementId: achievement.id,
            unlockedAt: Date.now(),
          });
          state.lastUnlocked = achievement;
          StorageManager.set(STORAGE_KEYS.USER_ACHIEVEMENTS, state.userAchievements);
          playSound('achievement');
        }
      });
    },
    checkPointsEarnedAchievements: (
      state,
      action: PayloadAction<{ profileId: string; totalPoints: number }>
    ) => {
      const { profileId, totalPoints } = action.payload;
      const pointAchievements = state.achievements.filter(
        (a) => a.condition.type === 'POINTS_EARNED' && a.condition.value <= totalPoints
      );

      pointAchievements.forEach((achievement) => {
        const isUnlocked = state.userAchievements.some(
          (ua) => ua.profileId === profileId && ua.achievementId === achievement.id
        );

        if (!isUnlocked) {
          state.userAchievements.push({
            profileId,
            achievementId: achievement.id,
            unlockedAt: Date.now(),
          });
          state.lastUnlocked = achievement;
          StorageManager.set(STORAGE_KEYS.USER_ACHIEVEMENTS, state.userAchievements);
          playSound('achievement');
        }
      });
    },
    checkStreakAchievements: (
      state,
      action: PayloadAction<{ profileId: string; streakDays: number }>
    ) => {
      const { profileId, streakDays } = action.payload;
      const streakAchievements = state.achievements.filter(
        (a) => a.condition.type === 'STREAK_MAINTAINED' && a.condition.value <= streakDays
      );

      streakAchievements.forEach((achievement) => {
        const isUnlocked = state.userAchievements.some(
          (ua) => ua.profileId === profileId && ua.achievementId === achievement.id
        );

        if (!isUnlocked) {
          state.userAchievements.push({
            profileId,
            achievementId: achievement.id,
            unlockedAt: Date.now(),
          });
          state.lastUnlocked = achievement;
          StorageManager.set(STORAGE_KEYS.USER_ACHIEVEMENTS, state.userAchievements);
          playSound('achievement');
        }
      });
    },
  },
});

// Selectors
export const selectAchievements = (state: RootState) => state.achievement.achievements;
export const selectUserAchievements = (state: RootState) => state.achievement.userAchievements;
export const selectLastUnlockedAchievement = (state: RootState) => state.achievement.lastUnlocked;

export const {
  unlockAchievement,
  clearLastUnlocked,
  checkTasksCompletedAchievements,
  checkPointsEarnedAchievements,
  checkStreakAchievements,
} = achievementSlice.actions;

export default achievementSlice.reducer;