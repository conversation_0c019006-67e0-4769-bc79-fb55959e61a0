import React, { useEffect } from 'react';
import {
  Box,
  Text,
  SimpleGrid,
  VStack,
  Button,
  useColorModeValue,
  Heading,
  HStack,
  Select,
  Spacer,
} from '@chakra-ui/react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { completeTask, createTask, resetTask } from '../../store/slices/taskSlice';
import { usePoints } from '../../hooks/usePoints';
import { useToast } from '@chakra-ui/react';
import { createPointsToast } from '../../components/PointsToast';
import { selectProfile } from '../../store/slices/profileSlice';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import { weeklyTasks, getPredefinedTasks, TaskDefinition } from './predefinedTasks';
import { TaskCategory } from '../../types/task';
import { dollarToPoints } from './predefinedTasks';

const TaskList = () => {
  const dispatch = useDispatch();
  const toast = useToast();
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const categoryLabels: Record<TaskCategory, string> = {
    chores: '🧹 Chores',
    learning: '📚 Learning',
    helping: '🤝 Helping Others',
    selfCare: '💚 Self Care',
  };

  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
  const todayIndex = dayNames.indexOf(today);

  const groupTasksByCategory = (tasks: TaskDefinition[]) => {
    const grouped = new Map<TaskCategory, TaskDefinition[]>();
    tasks.forEach(task => {
      if (!grouped.has(task.category)) {
        grouped.set(task.category, []);
      }
      grouped.get(task.category)?.push(task);
    });
    return grouped;
  };

  const isDayInFuture = (day: string) => {
    const dayIndex = dayNames.indexOf(day);
    if (dayIndex === -1) return false;
    // Handle week wraparound
    return dayIndex > todayIndex && (dayIndex - todayIndex) <= 3;
  };
  const selectedProfileId = useSelector((state: RootState) => state.profile.selectedProfileId);
  const allTasks = useSelector((state: RootState) => state.task.tasks);
  const profiles = useSelector((state: RootState) => state.profile.profiles);
  const { awardPoints } = usePoints(selectedProfileId || '');
  const bgColor = useColorModeValue('white', 'gray.800');
  const todayBgColor = useColorModeValue('blue.50', 'blue.900');

  const handleProfileChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(selectProfile(e.target.value));
  };

  useEffect(() => {
    if (selectedProfileId) {
      const predefinedTasks = getPredefinedTasks(selectedProfileId);
      predefinedTasks.forEach(taskData => {
        const existingTask = allTasks.find(t =>
          t.title === taskData.title &&
          t.assignedTo.includes(selectedProfileId)
        );
        if (!existingTask) {
          dispatch(createTask(taskData));
        }
      });
    }
  }, [selectedProfileId, dispatch, allTasks]);

  if (!selectedProfileId) {
    return <Text textAlign="center" color="gray.500">Select a profile to view tasks</Text>;
  }

  const handleBackToMenu = () => {
    dispatch(selectProfile(''));
  };

  return (
    <VStack spacing={6} align="stretch" w="full">
      <HStack spacing={4} w="full" mb={2}>
        <Button
          leftIcon={<ChevronLeftIcon />}
          variant="ghost"
          onClick={handleBackToMenu}
          size="sm"
        >
          Back to Menu
        </Button>
        <Spacer />
        <Select
          value={selectedProfileId}
          onChange={handleProfileChange}
          size="sm"
          w="200px"
        >
          {profiles.map(profile => (
            <option key={profile.id} value={profile.id}>
              {profile.name}
            </option>
          ))}
        </Select>
      </HStack>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
        {Object.entries(weeklyTasks).map(([day, dayTasks]) => {
          const isToday = day === today;
          return (
            <Box 
              key={day}
              p={4}
              bg={isToday ? todayBgColor : bgColor}
              borderRadius="xl"
              borderWidth="1px"
              borderColor={isToday ? 'blue.200' : 'gray.200'}
              shadow={isToday ? 'md' : 'sm'}
            >
              <VStack spacing={6} align="stretch">
                <Heading size="md" color={isToday ? 'blue.500' : 'gray.600'}>
                  {day} {isToday && '(Today)'}
                </Heading>
                {Array.from(groupTasksByCategory(dayTasks)).map(([category, categoryTasks]) => (
                  <VStack key={category} spacing={2} align="stretch">
                    <Heading size="sm" color="gray.600">
                      {categoryLabels[category]}
                    </Heading>
                    {categoryTasks.map((taskDef) => {
                      const taskTitle = `${taskDef.emoji} ${taskDef.title}`;
                      const isCompleted = allTasks.some(t =>
                        t.title === taskTitle &&
                        t.assignedTo.includes(selectedProfileId) &&
                        t.completed
                      );
                      return (
                        <Button
                          key={taskDef.title}
                          onClick={() => {
                            if (isDayInFuture(day)) return;
                            let task = allTasks.find(t =>
                              t.title === taskTitle &&
                              t.assignedTo.includes(selectedProfileId)
                            );
                            
                            if (!task) {
                              const newTaskData = {
                                title: taskTitle,
                                description: `Complete your ${taskDef.title.toLowerCase()} task for ${day}`,
                                reward: dollarToPoints(1.00),
                                assignedTo: [selectedProfileId],
                                category: taskDef.category,
                                emoji: taskDef.emoji
                              };
                              dispatch(createTask(newTaskData));
                              // Find the newly created task
                              task = allTasks.find(t =>
                                t.title === taskTitle &&
                                t.assignedTo.includes(selectedProfileId)
                              );
                            }

                            if (task) {
                              if (task.completed) {
                                dispatch(resetTask(task.id));
                              } else {
                                dispatch(completeTask(task.id));
                                // Award points for task completion
                                awardPoints(task.reward);
                                toast(createPointsToast(task.reward, task.title));
                              }
                            }
                          }}
                          isDisabled={isDayInFuture(day)}
                          colorScheme={isCompleted ? 'green' : 'gray'}
                          variant={isCompleted ? 'solid' : 'outline'}
                          width="full"
                          display="flex"
                          justifyContent="flex-start"
                          p={3}
                          title={isDayInFuture(day) ? "Cannot complete future tasks" : undefined}
                        >
                          <Text fontSize="xl" mr={3}>{taskDef.emoji}</Text>
                          <VStack align="flex-start" spacing={0}>
                            <HStack spacing={2}>
                              <Text>{taskDef.title}</Text>
                              <Text color="green.500" fontSize="sm">
                                (${taskDef.prize.toFixed(2)})
                              </Text>
                            </HStack>
                            {isCompleted && allTasks.find(t =>
                              t.title === taskTitle &&
                              t.assignedTo.includes(selectedProfileId))?.completedAt && (
                              <Text fontSize="xs" color="gray.500">
                                Completed: {new Date(allTasks.find(t =>
                                  t.title === taskTitle &&
                                  t.assignedTo.includes(selectedProfileId))?.completedAt!).toLocaleTimeString()}
                              </Text>
                            )}
                          </VStack>
                        </Button>
                      );
                    })}
                  </VStack>
                ))}
              </VStack>
            </Box>
          );
        })}
      </SimpleGrid>
    </VStack>
  );
};

export default TaskList;