import { useSelector } from 'react-redux';
import { RootState } from '../store';

interface StreakData {
  currentStreak: number;
  bestStreak: number;
  lastCompletedDate: Date | null;
}

export const useStreak = (profileId: string): StreakData => {
  const tasks = useSelector((state: RootState) => state.task.tasks);

  // Get completed tasks for the profile
  const completedTasks = tasks.filter(
    (task) => task.completed && 
    task.assignedTo.includes(profileId) && 
    task.completedAt
  );

  // Sort tasks by completion date
  const sortedDates = completedTasks
    .map(task => new Date(task.completedAt!))
    .sort((a, b) => b.getTime() - a.getTime());

  // Get unique dates (one completion per day counts)
  const uniqueDates = Array.from(new Set(
    sortedDates.map(date => date.toDateString())
  )).map(dateStr => new Date(dateStr));

  // Calculate current streak
  let currentStreak = 0;
  let bestStreak = 0;
  let tempStreak = 0;
  let lastCompletedDate: Date | null = uniqueDates[0] || null;

  // If no tasks completed or last completion was more than a day ago, streak is 0
  if (!lastCompletedDate || 
      (new Date().getTime() - lastCompletedDate.getTime()) > (24 * 60 * 60 * 1000)) {
    currentStreak = 0;
  } else {
    // Calculate streaks by checking consecutive days
    for (let i = 0; i < uniqueDates.length; i++) {
      const currentDate = uniqueDates[i];
      const nextDate = uniqueDates[i + 1];

      if (nextDate) {
        const dayDiff = (currentDate.getTime() - nextDate.getTime()) / (1000 * 60 * 60 * 24);
        if (dayDiff === 1) {
          tempStreak++;
          bestStreak = Math.max(bestStreak, tempStreak);
        } else {
          tempStreak = 1;
        }
      } else {
        tempStreak++;
        bestStreak = Math.max(bestStreak, tempStreak);
      }
    }

    // Current streak is the temp streak if the last completion was today or yesterday
    const today = new Date();
    const daysSinceLastCompletion = Math.floor(
      (today.getTime() - lastCompletedDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceLastCompletion <= 1) {
      currentStreak = tempStreak;
    }
  }

  return {
    currentStreak,
    bestStreak: Math.max(bestStreak, currentStreak),
    lastCompletedDate,
  };
};