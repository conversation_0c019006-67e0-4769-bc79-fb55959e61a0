import React from 'react';
import {
  Box,
  HStack,
  Icon,
  Text,
  keyframes,
} from '@chakra-ui/react';
import { FiStar } from 'react-icons/fi';
import { motion } from 'framer-motion';

const float = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
`;

const MotionBox = motion(Box);

interface PointsToastProps {
  points: number;
  taskTitle: string;
}

const PointsToast: React.FC<PointsToastProps> = ({ points, taskTitle }) => {
  return (
    <MotionBox
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      bg="linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)"
      color="white"
      p={3}
      borderRadius="lg"
      shadow="lg"
      maxW="300px"
    >
      <HStack spacing={3} align="center">
        <Box
          as={motion.div}
          animation={`${float} 2s ease-in-out infinite`}
        >
          <Icon as={FiStar} boxSize={5} color="yellow.300" />
        </Box>
        <Box>
          <Text fontWeight="bold" fontSize="md">
            +{points} points!
          </Text>
          <Text fontSize="xs" opacity={0.9}>
            {taskTitle}
          </Text>
        </Box>
      </HStack>
    </MotionBox>
  );
}

export const createPointsToast = (points: number, taskTitle: string) => ({
  position: 'top-right' as const,
  duration: 2000,
  render: () => <PointsToast points={points} taskTitle={taskTitle} />,
});