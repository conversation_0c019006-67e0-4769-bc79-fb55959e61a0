import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  VStack,
  Heading,
  useColorModeValue,
} from '@chakra-ui/react';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { selectAchievements, selectUserAchievements } from '../../store/slices/achievementSlice';
import { RootState } from '../../store';

const MotionBox = motion(Box);

const AchievementList = () => {
  const achievements = useSelector(selectAchievements);
  const userAchievements = useSelector(selectUserAchievements);
  const selectedProfileId = useSelector((state: RootState) => state.profile.selectedProfileId);
  
  const bgGradient = useColorModeValue(
    'linear(to-br, white, gray.50)',
    'linear(to-br, gray.800, gray.900)'
  );

  const unlockedAchievements = userAchievements.filter(
    ua => ua.profileId === selectedProfileId
  );

  return (
    <Box p={4}>
      <VStack spacing={6} align="stretch">
        <Heading size="lg" textAlign="center" color="primary.500">
          Achievements
        </Heading>
        <Text textAlign="center" color="gray.600">
          Track your progress and unlock special achievements!
        </Text>

        <SimpleGrid columns={{ base: 1, sm: 2, md: 3 }} spacing={4}>
          {achievements.map((achievement) => {
            const isUnlocked = unlockedAchievements.some(
              ua => ua.achievementId === achievement.id
            );
            const unlockedDate = unlockedAchievements.find(
              ua => ua.achievementId === achievement.id
            )?.unlockedAt;

            return (
              <MotionBox
                key={achievement.id}
                p={6}
                bgGradient={bgGradient}
                borderRadius="xl"
                boxShadow="md"
                border="1px solid"
                borderColor={isUnlocked ? "yellow.400" : "gray.200"}
                opacity={isUnlocked ? 1 : 0.7}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                position="relative"
                overflow="hidden"
                _before={isUnlocked ? {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, yellow.100, transparent)',
                  opacity: 0.3,
                  zIndex: 0,
                } : undefined}
              >
                <VStack spacing={3} align="center" position="relative" zIndex={1}>
                  <Text fontSize="3xl">{achievement.imageUrl}</Text>
                  <Heading size="md" textAlign="center">
                    {achievement.title}
                  </Heading>
                  <Text fontSize="sm" color="gray.500" textAlign="center">
                    {achievement.description}
                  </Text>
                  {isUnlocked && (
                    <Text fontSize="xs" color="green.500">
                      Unlocked {new Date(unlockedDate!).toLocaleDateString()}
                    </Text>
                  )}
                  {!isUnlocked && (
                    <Text fontSize="xs" color="gray.500">
                      {achievement.condition.type === 'TASKS_COMPLETED' && 
                        `Complete ${achievement.condition.value} tasks`}
                      {achievement.condition.type === 'POINTS_EARNED' && 
                        `Earn ${achievement.condition.value} points`}
                      {achievement.condition.type === 'STREAK_MAINTAINED' && 
                        `Maintain a ${achievement.condition.value}-day streak`}
                    </Text>
                  )}
                </VStack>
              </MotionBox>
            );
          })}
        </SimpleGrid>
      </VStack>
    </Box>
  );
};

export default AchievementList;