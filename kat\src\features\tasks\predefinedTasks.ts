import { CreateTaskData } from '../../types/task';

// Convert dollar amount to points ($1 = 100 points)
export const dollarToPoints = (amount: number) => Math.round(amount * 100);

import { TaskCategory } from '../../types/task';

export interface TaskDefinition {
  title: string;
  emoji: string;
  category: TaskCategory;
  prize: number; // Prize in dollars
}

type DailyTasks = {
  [key: string]: TaskDefinition[];
};

export const weeklyTasks: DailyTasks = {
  Monday: [
    { title: 'Make the bed', emoji: '🛏️', category: 'chores', prize: 0.50 },
    { title: 'Put away toys', emoji: '🧸', category: 'chores', prize: 0.50 },
    { title: 'Read a book', emoji: '📚', category: 'learning', prize: 1.00 },
    { title: 'Practice math', emoji: '🔢', category: 'learning', prize: 1.50 },
    { title: 'Set the table', emoji: '🍽️', category: 'helping', prize: 0.50 },
  ],
  Tuesday: [
    { title: 'Clear the table', emoji: '✨', category: 'chores', prize: 0.50 },
    { title: 'Draw a picture', emoji: '🎨', category: 'selfCare', prize: 0.75 },
    { title: 'Help a sibling', emoji: '🤝', category: 'helping', prize: 1.00 },
    { title: 'Water the plants', emoji: '🌱', category: 'chores', prize: 0.50 },
    { title: 'Complete a puzzle', emoji: '🧩', category: 'learning', prize: 1.00 },
  ],
  Wednesday: [
    { title: 'Make the bed', emoji: '🛏️', category: 'chores', prize: 0.50 },
    { title: 'Write a story', emoji: '✍️', category: 'learning', prize: 1.50 },
    { title: 'Feed the pet', emoji: '🐾', category: 'helping', prize: 0.75 },
    { title: 'Organize bookshelf', emoji: '📚', category: 'chores', prize: 1.00 },
    { title: 'Do something kind', emoji: '💝', category: 'helping', prize: 1.00 },
  ],
  Thursday: [
    { title: 'Put away toys', emoji: '🧸', category: 'chores', prize: 0.50 },
    { title: 'Practice math', emoji: '🔢', category: 'learning', prize: 1.50 },
    { title: 'Set the table', emoji: '🍽️', category: 'helping', prize: 0.50 },
    { title: 'Build with Legos', emoji: '🏗️', category: 'selfCare', prize: 0.75 },
    { title: 'Take out trash', emoji: '🗑️', category: 'chores', prize: 0.75 },
  ],
  Friday: [
    { title: 'Make the bed', emoji: '🛏️', category: 'chores', prize: 0.50 },
    { title: 'Clear the table', emoji: '✨', category: 'chores', prize: 0.50 },
    { title: 'Draw a picture', emoji: '🎨', category: 'selfCare', prize: 0.75 },
    { title: 'Vacuum a room', emoji: '🧹', category: 'chores', prize: 1.00 },
    { title: 'Write thank-you note', emoji: '💌', category: 'helping', prize: 1.00 },
  ],
  Saturday: [
    { title: 'Help with laundry', emoji: '👕', category: 'chores', prize: 1.00 },
    { title: 'Read a book', emoji: '📚', category: 'learning', prize: 1.00 },
    { title: 'Practice new skill', emoji: '⭐', category: 'learning', prize: 1.50 },
    { title: 'Put away toys', emoji: '🧸', category: 'chores', prize: 0.50 },
    { title: 'Help a sibling', emoji: '🤝', category: 'helping', prize: 1.00 },
  ],
  Sunday: [
    { title: 'Make the bed', emoji: '🛏️', category: 'chores', prize: 0.50 },
    { title: 'Feed the pet', emoji: '🐾', category: 'helping', prize: 0.75 },
    { title: 'Do something kind', emoji: '💝', category: 'helping', prize: 1.00 },
    { title: 'Set the table', emoji: '🍽️', category: 'helping', prize: 0.50 },
    { title: 'Draw a picture', emoji: '🎨', category: 'selfCare', prize: 0.75 },
  ],
};

export const getPredefinedTasks = (profileId: string): CreateTaskData[] => {
  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
  const todaysTasks = weeklyTasks[today] || {};

  return todaysTasks.map(task => ({
    title: `${task.emoji} ${task.title}`,
    description: `Complete your ${task.title.toLowerCase()} task for ${today}`,
    reward: dollarToPoints(task.prize),
    assignedTo: [profileId],
    category: task.category,
  }));
};