import React, { Suspense } from 'react'
import { ChakraProvider, ColorModeScript, Box, Heading, Text } from '@chakra-ui/react'
import { Provider as ReduxProvider } from 'react-redux'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { store } from './store'
import theme from './styles/theme'
import Layout from './components/Layout'
import ErrorBoundary from './components/ErrorBoundary'
import LoadingSpinner from './components/LoadingSpinner'
import KeyboardShortcuts from './components/KeyboardShortcuts'

// Lazy load routes
const Home = React.lazy(() => import('./features/home/<USER>'))
const ProfileSelection = React.lazy(() => import('./features/profiles/ProfileSelection'))
const ParentDashboard = React.lazy(() => import('./features/parent/ParentDashboard'))
const AchievementNotification = React.lazy(() => import('./features/achievements/AchievementNotification'))

function App() {
  return (
    <React.StrictMode>
      <ErrorBoundary>
        <ChakraProvider theme={theme}>
          <ColorModeScript initialColorMode={theme.config.initialColorMode} />
          <ReduxProvider store={store}>
            <Router>
              <Layout>
                <Suspense fallback={<LoadingSpinner fullScreen message="Loading application..." />}>
                  <Routes>
                    <Route path="/" element={<Home />} />
                    <Route path="/parent" element={<ParentDashboard />} />
                  </Routes>
                  <AchievementNotification />
                  <KeyboardShortcuts />
                </Suspense>
              </Layout>
            </Router>
          </ReduxProvider>
        </ChakraProvider>
      </ErrorBoundary>
    </React.StrictMode>
  )
}

export default App