{"name": "kat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "devDependencies": {"@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.7.2", "vite": "^6.2.0"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@chakra-ui/system": "^2.6.2", "@chakra-ui/theme-tools": "^2.1.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^2.6.0", "framer-motion": "^6.5.1", "idb": "^8.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "uuid": "^11.1.0"}}