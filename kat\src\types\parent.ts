export interface ParentSettings {
  pin: string;
  isLocked: boolean;
  allowedFeatures: {
    taskCreation: boolean;
    profileCreation: boolean;
    rewardRedemption: boolean;
  };
}

export interface PinValidation {
  isValid: boolean;
  attemptsLeft?: number;
  isTemporarilyLocked?: boolean;
  lockExpiresAt?: number;
}

export interface RewardItem {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  isAvailable: boolean;
  imageUrl?: string;
  requiresApproval: boolean;
}