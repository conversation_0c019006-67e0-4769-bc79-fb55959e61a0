import {
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Textarea,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Drawer,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON>eader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  useToast,
  Select,
  DrawerFooter,
  Box,
} from '@chakra-ui/react'
import { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParentControls } from '../../hooks/useParentControls'
import type { RootState } from '../../store'
import { createTask } from '../../store/slices/taskSlice'
import type { CreateTaskData, TaskCategory } from '../../types/task'

interface TaskFormProps {
  isOpen: boolean
  onClose: () => void
}

const TaskForm = ({ isOpen, onClose }: TaskFormProps) => {
  const dispatch = useDispatch()
  const toast = useToast()
  const { canCreateTasks } = useParentControls()

  useEffect(() => {
    if (isOpen && !canCreateTasks) {
      toast({
        title: 'Access Denied',
        description: 'Task creation is disabled in parent controls',
        status: 'error',
        duration: 3000,
      })
      onClose()
    }
  }, [isOpen, canCreateTasks, toast, onClose])

  if (!canCreateTasks) {
    return null
  }
  const profiles = useSelector((state: RootState) => state.profile.profiles)
  
  const [formData, setFormData] = useState<CreateTaskData>({
    title: '',
    description: '',
    reward: 5,
    assignedTo: [],
    category: 'chores',
  })

  const categoryOptions: { value: TaskCategory; label: string }[] = [
    { value: 'chores', label: '🧹 Chores' },
    { value: 'learning', label: '📚 Learning' },
    { value: 'helping', label: '🤝 Helping Others' },
    { value: 'selfCare', label: '💚 Self Care' },
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast({
        title: 'Title is required',
        status: 'error',
        duration: 2000,
      })
      return
    }

    if (formData.assignedTo.length === 0) {
      toast({
        title: 'Please assign the task to at least one profile',
        status: 'error',
        duration: 2000,
      })
      return
    }

    dispatch(createTask(formData))
    toast({
      title: 'Task created successfully',
      status: 'success',
      duration: 2000,
    })
    onClose()
    setFormData({
      title: '',
      description: '',
      reward: 5,
      assignedTo: [],
      category: 'chores',
    })
  }

  return (
    <Drawer 
      isOpen={isOpen} 
      onClose={onClose} 
      placement="right" 
      size={{ base: "full", md: "md" }}
    >
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton size="lg" />
        <DrawerHeader fontSize={{ base: "xl", md: "2xl" }}>Create New Task</DrawerHeader>

        <DrawerBody>
          <Box as="form" id="task-form" onSubmit={handleSubmit}>
            <VStack spacing={{ base: 6, md: 4 }}>
              <FormControl isRequired>
                <FormLabel fontSize={{ base: "md", md: "lg" }}>Title</FormLabel>
                <Input
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  placeholder="Enter task title"
                  size="lg"
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize={{ base: "md", md: "lg" }}>Description</FormLabel>
                <Textarea
                  value={formData.description || ''}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Enter task description (optional)"
                  size="lg"
                  minH="100px"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontSize={{ base: "md", md: "lg" }}>Category</FormLabel>
                <Select
                  value={formData.category}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      category: e.target.value as TaskCategory,
                    })
                  }
                  size="lg"
                >
                  {categoryOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Select>
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontSize={{ base: "md", md: "lg" }}>Reward Points</FormLabel>
                <NumberInput
                  value={formData.reward}
                  onChange={(valueString) =>
                    setFormData({
                      ...formData,
                      reward: parseInt(valueString) || 0,
                    })
                  }
                  min={1}
                  max={100}
                  size="lg"
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontSize={{ base: "md", md: "lg" }}>Assign To</FormLabel>
                <Select
                  value={formData.assignedTo[0] || ''}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      assignedTo: e.target.value ? [e.target.value] : [],
                    })
                  }
                  placeholder="Select profile"
                  size="lg"
                >
                  {profiles.map((profile) => (
                    <option key={profile.id} value={profile.id}>
                      {profile.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </VStack>
          </Box>
        </DrawerBody>

        <DrawerFooter>
          <Button 
            variant="outline" 
            mr={3} 
            onClick={onClose}
            size={{ base: "lg", md: "md" }}
          >
            Cancel
          </Button>
          <Button 
            colorScheme="primary" 
            type="submit" 
            form="task-form"
            size={{ base: "lg", md: "md" }}
          >
            Create Task
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default TaskForm