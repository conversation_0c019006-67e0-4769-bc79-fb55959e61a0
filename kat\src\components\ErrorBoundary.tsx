import React, { Component, ErrorInfo } from 'react';
import {
  VStack,
  Heading,
  Text,
  Button,
  Container,
  Icon,
  Box,
} from '@chakra-ui/react';
import { FiAlertTriangle } from 'react-icons/fi';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });
    
    // Here you could log the error to an error reporting service
    console.error('Uncaught error:', error, errorInfo);
  }

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    if (this.state.hasError) {
      return (
        <Container maxW="xl" py={12}>
          <VStack spacing={6} align="center">
            <Icon
              as={FiAlertTriangle}
              boxSize={12}
              color="red.500"
            />
            <Heading size="lg" color="red.500">
              Oops! Something went wrong
            </Heading>
            <Text color="gray.600" textAlign="center">
              We're sorry for the inconvenience. Please try again or reload the page.
            </Text>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <Box
                p={4}
                bg="gray.50"
                borderRadius="md"
                w="100%"
                maxH="200px"
                overflowY="auto"
              >
                <Text
                  as="pre"
                  fontFamily="mono"
                  fontSize="sm"
                  whiteSpace="pre-wrap"
                >
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </Text>
              </Box>
            )}

            <VStack spacing={4} pt={4}>
              <Button
                colorScheme="primary"
                onClick={this.handleReset}
              >
                Try Again
              </Button>
              <Button
                variant="ghost"
                onClick={this.handleReload}
              >
                Reload Page
              </Button>
            </VStack>
          </VStack>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;