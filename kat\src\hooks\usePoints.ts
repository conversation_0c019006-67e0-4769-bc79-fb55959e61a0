import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { addPoints } from '../store/slices/profileSlice';
import { calculateLevel } from '../components/PointsDisplay';

export const usePoints = (profileId: string) => {
  const dispatch = useDispatch();
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [newLevel, setNewLevel] = useState(1);
  
  const points = useSelector((state: RootState) =>
    state.profile.profiles.find(p => p.id === profileId)?.points || 0
  );
  
  const { level: currentLevel } = calculateLevel(points);
  
  const awardPoints = useCallback((amount: number) => {
    if (!profileId) return;
    
    const newPoints = points + amount;
    const { level: newLevelValue } = calculateLevel(newPoints);
    
    dispatch(addPoints({ profileId, points: amount }));
    
    // Check for level up
    if (newLevelValue > currentLevel) {
      setNewLevel(newLevelValue);
      setShowLevelUp(true);
    }
  }, [dispatch, profileId, points, currentLevel]);
  
  const closeLevelUpNotification = useCallback(() => {
    setShowLevelUp(false);
  }, []);
  
  // Reset notification state when profile changes
  useEffect(() => {
    setShowLevelUp(false);
    setNewLevel(1);
  }, [profileId]);
  
  return {
    points,
    currentLevel,
    showLevelUp,
    newLevel,
    awardPoints,
    closeLevelUpNotification,
  };
};

// Point values for different actions
export const POINT_VALUES = {
  TASK_COMPLETION: 50,
  STREAK_MILESTONE: 100,
  ACHIEVEMENT_UNLOCK: 200,
} as const;