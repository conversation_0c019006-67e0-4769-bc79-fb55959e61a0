import { useState, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';

interface AsyncOperationOptions {
  successMessage?: string;
  errorMessage?: string;
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
}

export const useAsyncOperation = <T extends (...args: any[]) => Promise<any>>(
  operation: T,
  options: AsyncOperationOptions = {}
) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const toast = useToast();

  const {
    successMessage = 'Operation completed successfully',
    errorMessage = 'An error occurred',
    showSuccessToast = false,
    showErrorToast = true,
  } = options;

  const execute = useCallback(
    async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await operation(...args);
        
        if (showSuccessToast) {
          toast({
            title: 'Success',
            description: successMessage,
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
        }

        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');
        setError(error);

        if (showErrorToast) {
          toast({
            title: 'Error',
            description: errorMessage || error.message,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        }

        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [operation, successMessage, errorMessage, showSuccessToast, showErrorToast, toast]
  );

  return {
    execute,
    isLoading,
    error,
    reset: () => {
      setError(null);
      setIsLoading(false);
    },
  };
};