import { Middleware, Action, MiddlewareAPI, Dispatch } from '@reduxjs/toolkit';
import type { RootState } from '../index';
import {
  checkTasksCompletedAchievements,
  checkPointsEarnedAchievements,
  checkStreakAchievements,
} from '../slices/achievementSlice';
import { playSound } from '../../utils/sounds';

interface CompleteTaskAction extends Action {
  type: 'task/completeTask';
  payload: string;
}

interface AddPointsAction extends Action {
  type: 'profile/addPoints';
  payload: {
    profileId: string;
    points: number;
  };
}

const isCompleteTaskAction = (action: unknown): action is CompleteTaskAction =>
  typeof action === 'object' && 
  action !== null && 
  'type' in action && 
  action.type === 'task/completeTask';

const isAddPointsAction = (action: unknown): action is AddPointsAction =>
  typeof action === 'object' && 
  action !== null && 
  'type' in action && 
  action.type === 'profile/addPoints';

export const achievementMiddleware: Middleware = 
  (store: MiddlewareAPI<Dispatch, RootState>) =>
  (next) =>
  (action: unknown) => {
    const result = next(action);
    const state = store.getState();

    // Handle task completion
    if (isCompleteTaskAction(action)) {
      const { selectedProfileId } = state.profile;
      if (selectedProfileId) {
        // Play task completion sound
        playSound('task');

        const completedTasks = state.task.tasks.filter(
          (task) =>
            task.completed && task.assignedTo.includes(selectedProfileId)
        ).length;

        // Check for achievements after a small delay to let the task sound finish
        setTimeout(() => {
          store.dispatch(
            checkTasksCompletedAchievements({
              profileId: selectedProfileId,
              completedCount: completedTasks,
            })
          );
        }, 500);
      }
    }

    // Handle points earned
    if (isAddPointsAction(action)) {
      const { profileId } = action.payload;
      const profile = state.profile.profiles.find((p) => p.id === profileId);
      
      if (profile) {
        store.dispatch(
          checkPointsEarnedAchievements({
            profileId,
            totalPoints: profile.points,
          })
        );
      }
    }

    // Check for streaks when tasks are completed
    if (isCompleteTaskAction(action)) {
      const { selectedProfileId } = state.profile;
      if (selectedProfileId) {
        const tasks = state.task.tasks.filter(
          (task) => task.assignedTo.includes(selectedProfileId)
        );

        // Calculate streak by checking if tasks were completed in consecutive days
        const dates = tasks
          .filter((task) => task.completed && task.completedAt)
          .map((task) => new Date(task.completedAt!).toDateString());

        const uniqueDates = [...new Set(dates)].sort();
        let currentStreak = 1;
        let maxStreak = 1;

        for (let i = 1; i < uniqueDates.length; i++) {
          const prevDate = new Date(uniqueDates[i - 1]);
          const currentDate = new Date(uniqueDates[i]);
          const dayDiff = (currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24);

          if (dayDiff === 1) {
            currentStreak++;
            maxStreak = Math.max(maxStreak, currentStreak);
          } else {
            currentStreak = 1;
          }
        }

        store.dispatch(
          checkStreakAchievements({
            profileId: selectedProfileId,
            streakDays: maxStreak,
          })
        );
      }
    }

    return result;
  };