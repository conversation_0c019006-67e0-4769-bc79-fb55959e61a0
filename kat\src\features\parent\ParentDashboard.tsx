import React, { useState, useRef } from 'react';
import {
  Box,
  VStack,
  Heading,
  FormControl,
  FormLabel,
  Switch,
  Button,
  Text,
  Card,
  CardHeader,
  CardBody,
  HStack,
  Icon,
  useToast,
  Divider,
  Container,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  SimpleGrid,
  Avatar,
} from '@chakra-ui/react';
import { useDispatch, useSelector } from 'react-redux';
import { FiSettings, FiLock, FiUsers, FiTrash2 } from 'react-icons/fi';
import PinProtection from './PinProtection';
import {
  selectParentSettings,
  selectIsParentLocked,
  updateAllowedFeatures,
  setPin,
  lockParentControls,
} from '../../store/slices/parentSlice';
import { deleteProfile } from '../../store/slices/profileSlice';
import type { RootState } from '../../store';

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
}) => {
  const cancelRef = useRef<HTMLButtonElement>(null) as React.RefObject<HTMLButtonElement>;

  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={cancelRef}
      onClose={onClose}
    >
      <AlertDialogOverlay>
        <AlertDialogContent>
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            {title}
          </AlertDialogHeader>

          <AlertDialogBody>
            {message}
          </AlertDialogBody>

          <AlertDialogFooter>
            <Button ref={cancelRef} onClick={onClose}>
              No
            </Button>
            <Button colorScheme="red" onClick={onConfirm} ml={3}>
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};

const ParentDashboard = () => {
  const dispatch = useDispatch();
  const toast = useToast();
  const settings = useSelector(selectParentSettings);
  const isLocked = useSelector(selectIsParentLocked);
  const profiles = useSelector((state: RootState) => state.profile.profiles);
  const [changingPin, setChangingPin] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState<string | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });

  const showConfirmDialog = (title: string, message: string, onConfirm: () => void) => {
    setConfirmDialog({
      isOpen: true,
      title,
      message,
      onConfirm,
    });
  };

  const closeConfirmDialog = () => {
    setConfirmDialog(prev => ({ ...prev, isOpen: false }));
  };

  const handleFeatureToggle = (feature: keyof typeof settings.allowedFeatures) => {
    const newState = !settings.allowedFeatures[feature];
    const action = newState ? 'enable' : 'disable';
    const featureName = feature.split(/(?=[A-Z])/).join(' ').toLowerCase();

    showConfirmDialog(
      `${action.charAt(0).toUpperCase() + action.slice(1)} ${featureName}?`,
      `Are you sure you want to ${action} ${featureName}?`,
      () => {
        dispatch(updateAllowedFeatures({ [feature]: newState }));
        toast({
          title: `${featureName.charAt(0).toUpperCase() + featureName.slice(1)} ${newState ? 'enabled' : 'disabled'}`,
          status: 'success',
          duration: 2000,
        });
        closeConfirmDialog();
      }
    );
  };

  const handleSetNewPin = () => {
    showConfirmDialog(
      'Change PIN',
      'Are you sure you want to change your PIN?',
      () => {
        setChangingPin(true);
        closeConfirmDialog();
      }
    );
  };

  const handleLock = () => {
    showConfirmDialog(
      'Lock Dashboard',
      'Are you sure you want to lock the parent dashboard?',
      () => {
        dispatch(lockParentControls());
        closeConfirmDialog();
      }
    );
  };

  const handleDeleteProfile = (profileId: string) => {
    const profile = profiles.find(p => p.id === profileId);
    if (!profile) return;

    showConfirmDialog(
      'Delete Profile',
      `Are you sure you want to delete ${profile.name}'s profile? This action cannot be undone.`,
      () => {
        dispatch(deleteProfile(profileId));
        toast({
          title: 'Profile Deleted',
          description: `${profile.name}'s profile has been deleted.`,
          status: 'success',
          duration: 3000,
        });
        closeConfirmDialog();
      }
    );
  };

  if (isLocked) {
    return (
      <Container maxW="xl" py={8}>
        <VStack spacing={6} align="center">
          <Icon as={FiLock} boxSize={8} />
          <Heading size="md">Parent Dashboard</Heading>
          <Text color="gray.600" textAlign="center">
            Enter your PIN to access parent controls
          </Text>
        </VStack>
      </Container>
    );
  }

  return (
    <>
      <Container maxW="4xl" py={{ base: 4, md: 8 }}>
        <VStack spacing={{ base: 4, md: 6 }} align="stretch" w="full">
          <HStack justify="space-between" wrap="wrap" gap={4}>
            <Heading size={{ base: "md", md: "lg" }}>Parent Dashboard</Heading>
            <Button
              leftIcon={<FiLock />}
              variant="ghost"
              onClick={handleLock}
              size={{ base: "sm", md: "md" }}
            >
              Lock
            </Button>
          </HStack>

          <Card variant="outline" shadow="md">
            <CardHeader bg="gray.50" borderTopRadius="md">
              <HStack spacing={3}>
                <Icon as={FiUsers} boxSize={{ base: 4, md: 5 }} />
                <Heading size={{ base: "sm", md: "md" }}>Profile Management</Heading>
              </HStack>
            </CardHeader>
            <CardBody py={6}>
              <VStack spacing={6} align="stretch">
                <Text fontSize="sm" color="gray.600" mb={4}>
                  Manage user profiles. Be careful when deleting profiles - this action cannot be undone.
                </Text>
                
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                  {profiles.map((profile) => (
                    <HStack
                      key={profile.id}
                      p={4}
                      bg="gray.50"
                      borderRadius="md"
                      justify="space-between"
                      align="center"
                    >
                      <HStack>
                        <Avatar
                          size="sm"
                          bg={profile.avatarColor}
                          name={profile.name}
                        />
                        <Box>
                          <Text fontWeight="bold">{profile.name}</Text>
                          <Text fontSize="sm" color="gray.500">
                            {profile.points} points
                          </Text>
                        </Box>
                      </HStack>
                      <Button
                        size="sm"
                        colorScheme="red"
                        variant="ghost"
                        leftIcon={<Icon as={FiTrash2} />}
                        onClick={() => handleDeleteProfile(profile.id)}
                      >
                        Delete
                      </Button>
                    </HStack>
                  ))}
                </SimpleGrid>
              </VStack>
            </CardBody>
          </Card>

          <Card variant="outline" shadow="md">
            <CardHeader bg="gray.50" borderTopRadius="md">
              <HStack spacing={3}>
                <Icon as={FiSettings} boxSize={{ base: 4, md: 5 }} />
                <Heading size={{ base: "sm", md: "md" }}>Feature Controls</Heading>
              </HStack>
            </CardHeader>
            <CardBody py={6}>
              <VStack spacing={6} align="stretch">
                <Text fontSize="sm" color="gray.600" mb={4}>
                  Control which features are available to users. Toggle switches to enable or disable functionality.
                </Text>
                
                <FormControl 
                  display="flex" 
                  alignItems="flex-start"
                  flexDirection="column"
                  p={4}
                  bg="gray.50"
                  borderRadius="md"
                >
                  <HStack width="full" justify="space-between" align="center">
                    <Box flex="1">
                      <FormLabel htmlFor="task-creation" mb={0}>Task Creation</FormLabel>
                      <Text fontSize="sm" color="gray.600">Allow users to create and manage tasks</Text>
                    </Box>
                    <Switch
                      id="task-creation"
                      size="lg"
                      colorScheme="primary"
                      isChecked={settings.allowedFeatures.taskCreation}
                      onChange={() => handleFeatureToggle('taskCreation')}
                    />
                  </HStack>
                </FormControl>

                <FormControl 
                  display="flex" 
                  alignItems="flex-start"
                  flexDirection="column"
                  p={4}
                  bg="gray.50"
                  borderRadius="md"
                >
                  <HStack width="full" justify="space-between" align="center">
                    <Box flex="1">
                      <FormLabel htmlFor="profile-creation" mb={0}>Profile Creation</FormLabel>
                      <Text fontSize="sm" color="gray.600">Allow creation of new user profiles</Text>
                    </Box>
                    <Switch
                      id="profile-creation"
                      size="lg"
                      colorScheme="primary"
                      isChecked={settings.allowedFeatures.profileCreation}
                      onChange={() => handleFeatureToggle('profileCreation')}
                    />
                  </HStack>
                </FormControl>

                <FormControl 
                  display="flex" 
                  alignItems="flex-start"
                  flexDirection="column"
                  p={4}
                  bg="gray.50"
                  borderRadius="md"
                >
                  <HStack width="full" justify="space-between" align="center">
                    <Box flex="1">
                      <FormLabel htmlFor="reward-redemption" mb={0}>Reward Redemption</FormLabel>
                      <Text fontSize="sm" color="gray.600">Allow users to redeem points for rewards</Text>
                    </Box>
                    <Switch
                      id="reward-redemption"
                      size="lg"
                      colorScheme="primary"
                      isChecked={settings.allowedFeatures.rewardRedemption}
                      onChange={() => handleFeatureToggle('rewardRedemption')}
                    />
                  </HStack>
                </FormControl>
              </VStack>
            </CardBody>
          </Card>

          <Card variant="outline" shadow="md">
            <CardHeader bg="gray.50" borderTopRadius="md">
              <HStack spacing={3}>
                <Icon as={FiLock} boxSize={{ base: 4, md: 5 }} />
                <Heading size={{ base: "sm", md: "md" }}>Security</Heading>
              </HStack>
            </CardHeader>
            <CardBody py={6}>
              <VStack spacing={4} align="stretch">
                <Text color="gray.600">Change your PIN to protect parent settings</Text>
                <Button
                  onClick={handleSetNewPin}
                  colorScheme="primary"
                  size={{ base: "sm", md: "md" }}
                  maxW="200px"
                >
                  Change PIN
                </Button>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={closeConfirmDialog}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
      />
    </>
  );
};

export default ParentDashboard;