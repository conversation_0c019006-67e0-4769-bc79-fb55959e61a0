:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  min-height: 100vh;
  width: 100%;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Fix for iOS Safari's viewport height issue */
@supports (-webkit-touch-callout: none) {
  html,
  body,
  #root {
    min-height: -webkit-fill-available;
  }
}