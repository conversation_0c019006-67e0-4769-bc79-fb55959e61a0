import React from 'react';
import {
  Box,
  Text,
  HStack,
  VStack,
  Icon,
  Tooltip,
} from '@chakra-ui/react';
import { FiZap } from 'react-icons/fi';
import { motion } from 'framer-motion';

const MotionIcon = motion(Icon);

interface StreakTrackerProps {
  currentStreak: number;
  bestStreak: number;
}

const StreakTracker: React.FC<StreakTrackerProps> = ({ currentStreak, bestStreak }) => {
  return (
    <Box
      bg="white"
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      border="1px solid"
      borderColor="gray.100"
    >
      <VStack spacing={2} align="stretch">
        <HStack spacing={4} justify="center">
          <Tooltip label="Current Streak" placement="top">
            <VStack spacing={1}>
              <MotionIcon
                as={FiZap}
                color="orange.500"
                boxSize={6}
                animate={currentStreak > 0 ? {
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0],
                } : {}}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
              <Text
                fontSize="xl"
                fontWeight="bold"
                color="orange.500"
              >
                {currentStreak}
              </Text>
              <Text fontSize="xs" color="gray.500">
                Current
              </Text>
            </VStack>
          </Tooltip>

          <Tooltip label="Best Streak" placement="top">
            <VStack spacing={1}>
              <Icon
                as={FiZap}
                color="purple.500"
                boxSize={6}
              />
              <Text
                fontSize="xl"
                fontWeight="bold"
                color="purple.500"
              >
                {bestStreak}
              </Text>
              <Text fontSize="xs" color="gray.500">
                Best
              </Text>
            </VStack>
          </Tooltip>
        </HStack>

        <Text
          textAlign="center"
          fontSize="sm"
          color="gray.600"
          mt={2}
        >
          {currentStreak > 0
            ? `${currentStreak} day${currentStreak === 1 ? '' : 's'} streak! Keep it up! ⚡`
            : 'Complete tasks daily to build your streak! ⚡'}
        </Text>
      </VStack>
    </Box>
  );
};

export default StreakTracker;