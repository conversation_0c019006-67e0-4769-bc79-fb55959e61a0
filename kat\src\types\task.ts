export type TaskCategory = 'chores' | 'learning' | 'helping' | 'selfCare';

export interface Task {
  id: string
  title: string
  description?: string
  reward: number
  assignedTo: string[]
  completed: boolean
  completedAt?: number
  createdAt: number
  category: TaskCategory
}

export interface CreateTaskData {
  title: string
  description?: string
  reward: number
  assignedTo: string[]
  category: TaskCategory
}

export interface UpdateTaskData {
  title?: string
  description?: string
  reward?: number
  assignedTo?: string[]
  completed?: boolean
  completedAt?: number
  category?: TaskCategory
}