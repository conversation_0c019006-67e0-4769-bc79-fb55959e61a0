import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { v4 as uuidv4 } from 'uuid'
import type { Profile, CreateProfileData, UpdateProfileData } from '../../types/profile'
import { STORAGE_KEYS, StorageManager } from '../../utils/storage'

interface ProfileState {
  profiles: Profile[]
  selectedProfileId: string | null
  isLoading: boolean
  error: string | null
}

const initialState: ProfileState = {
  profiles: StorageManager.get<Profile[]>(STORAGE_KEYS.PROFILES, []),
  selectedProfileId: StorageManager.get<string | null>(STORAGE_KEYS.SELECTED_PROFILE, null),
  isLoading: false,
  error: null,
}

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    createProfile: (state, action: PayloadAction<CreateProfileData>) => {
      const newProfile: Profile = {
        id: uuidv4(),
        name: action.payload.name,
        avatarColor: action.payload.avatarColor,
        createdAt: Date.now(),
        points: 0,
        achievements: [],
        completedTasks: [],
      }
      state.profiles.push(newProfile)
      StorageManager.set(STORAGE_KEYS.PROFILES, state.profiles)
    },
    selectProfile: (state, action: PayloadAction<string>) => {
      state.selectedProfileId = action.payload
      StorageManager.set(STORAGE_KEYS.SELECTED_PROFILE, action.payload)
    },
    updateProfile: (state, action: PayloadAction<{ id: string; data: UpdateProfileData }>) => {
      const profile = state.profiles.find(p => p.id === action.payload.id)
      if (profile) {
        Object.assign(profile, action.payload.data)
        StorageManager.set(STORAGE_KEYS.PROFILES, state.profiles)
      }
    },
    deleteProfile: (state, action: PayloadAction<string>) => {
      state.profiles = state.profiles.filter(p => p.id !== action.payload)
      if (state.selectedProfileId === action.payload) {
        state.selectedProfileId = null
      }
      StorageManager.set(STORAGE_KEYS.PROFILES, state.profiles)
      StorageManager.set(STORAGE_KEYS.SELECTED_PROFILE, state.selectedProfileId)
    },
    addPoints: (state, action: PayloadAction<{ profileId: string; points: number }>) => {
      const profile = state.profiles.find(p => p.id === action.payload.profileId)
      if (profile) {
        profile.points += action.payload.points
        StorageManager.set(STORAGE_KEYS.PROFILES, state.profiles)
      }
    },
  },
})

export const { 
  createProfile, 
  selectProfile, 
  updateProfile, 
  deleteProfile,
  addPoints,
} = profileSlice.actions

export default profileSlice.reducer