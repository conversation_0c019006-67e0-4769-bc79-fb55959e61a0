import React, { useEffect } from 'react';
import {
  Box,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Text,
  Grid,
  GridItem,
  Kbd,
  VStack,
} from '@chakra-ui/react';

interface Shortcut {
  key: string;
  description: string;
  modifier?: 'ctrl' | 'shift' | 'alt';
}

const GLOBAL_SHORTCUTS: Shortcut[] = [
  { key: '?', description: 'Show keyboard shortcuts' },
  { key: 'Esc', description: 'Close modal or return to previous screen' },
  { key: 'h', description: 'Return to home', modifier: 'alt' },
  { key: 'p', description: 'Open parent dashboard', modifier: 'ctrl' },
  { key: 'n', description: 'Create new task', modifier: 'ctrl' },
];

export const KeyboardShortcuts: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Show shortcuts modal when '?' is pressed
      if (e.key === '?' && !e.ctrlKey && !e.altKey && !e.metaKey) {
        e.preventDefault();
        onOpen();
      }

      // Handle other global shortcuts
      GLOBAL_SHORTCUTS.forEach(shortcut => {
        const matchesKey = e.key.toLowerCase() === shortcut.key.toLowerCase();
        const matchesModifier = shortcut.modifier ? {
          ctrl: e.ctrlKey,
          shift: e.shiftKey,
          alt: e.altKey,
        }[shortcut.modifier] : true;

        if (matchesKey && matchesModifier) {
          e.preventDefault();
          handleShortcut(shortcut);
        }
      });
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [onOpen]);

  const handleShortcut = (shortcut: Shortcut) => {
    switch (shortcut.key) {
      case 'h':
        window.location.href = '/home';
        break;
      case 'p':
        window.location.href = '/parent';
        break;
      // Add more shortcut handlers as needed
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Keyboard Shortcuts</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={4} align="stretch">
            <Text mb={4}>
              Press <Kbd>?</Kbd> anywhere to show this help dialog.
            </Text>

            <Grid templateColumns="1fr 2fr" gap={4}>
              {GLOBAL_SHORTCUTS.map((shortcut) => (
                <React.Fragment key={`${shortcut.modifier || ''}-${shortcut.key}`}>
                  <GridItem>
                    <Box>
                      {shortcut.modifier && (
                        <>
                          <Kbd>{shortcut.modifier}</Kbd>
                          {' + '}
                        </>
                      )}
                      <Kbd>{shortcut.key}</Kbd>
                    </Box>
                  </GridItem>
                  <GridItem>
                    <Text>{shortcut.description}</Text>
                  </GridItem>
                </React.Fragment>
              ))}
            </Grid>

            <Box mt={4} p={4} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600">
                Tip: These shortcuts are designed to work across the entire application.
                Use <Kbd>Esc</Kbd> to close any modal or return to the previous screen.
              </Text>
            </Box>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default KeyboardShortcuts;